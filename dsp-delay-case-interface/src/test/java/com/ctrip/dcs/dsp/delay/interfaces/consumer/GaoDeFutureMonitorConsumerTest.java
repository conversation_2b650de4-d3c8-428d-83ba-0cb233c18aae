package com.ctrip.dcs.dsp.delay.interfaces.consumer;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;

import java.util.*;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import com.ctrip.dcs.dsp.delay.gateway.GeoGateway;
import com.ctrip.dcs.dsp.delay.infrastructure.dto.GaoDeFutureDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.util.MonitorUtil;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.util.JsonUtil;

import qunar.tc.qmq.Message;

/**
 * GaoDeFutureMonitorConsumer 单元测试
 * 
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({JsonUtil.class, MonitorUtil.class})
public class GaoDeFutureMonitorConsumerTest {

    @InjectMocks
    private GaoDeFutureMonitorConsumer gaoDeFutureMonitorConsumer;

    @Mock
    private GeoGateway geoGateway;

    @Mock
    private Message message;

    private GaoDeFutureDTO testGaoDeFutureDTO;
    private Route testRoute;
    private String testJson;

    @Before
    public void setUp() {
        // 创建测试数据
        testGaoDeFutureDTO = new GaoDeFutureDTO();
        testGaoDeFutureDTO.setCityId(1);
        testGaoDeFutureDTO.setUserOrderId("test-order-123");
        testGaoDeFutureDTO.setFromLongitude(121.5);
        testGaoDeFutureDTO.setFromLatitude(31.2);
        testGaoDeFutureDTO.setToLongitude(121.6);
        testGaoDeFutureDTO.setToLatitude(31.3);
        testGaoDeFutureDTO.setDepartureTime(new Date());
        testGaoDeFutureDTO.setRealTimeDistance(10.0);
        testGaoDeFutureDTO.setRealTimeDuration(20.0);
        testGaoDeFutureDTO.setFutureDistance(12.0);
        testGaoDeFutureDTO.setFutureDuration(25.0);

        testRoute = new Route("test-hash", 11.5, 22.5);

        testJson = JsonUtil.toJson(testGaoDeFutureDTO);
    }

    @Test
    public void testOnMessage_SuccessfulProcessing() {
        // 模拟静态方法
        mockStatic(JsonUtil.class);
        mockStatic(MonitorUtil.class);
        
        // 准备测试数据
        when(message.getStringProperty("data")).thenReturn(testJson);
        when(geoGateway.queryRoutes(eq(1), any(List.class))).thenReturn(Arrays.asList(testRoute));
        when(JsonUtil.fromJson(eq(testJson), any())).thenReturn(testGaoDeFutureDTO);

        // 执行测试
        gaoDeFutureMonitorConsumer.onMessage(message);

        // 验证调用
        verify(message, times(1)).getStringProperty("data");
        verify(geoGateway, times(1)).queryRoutes(eq(1), any(List.class));
    }

    @Test
    public void testOnMessage_JsonDeserializationReturnsNull() {
        // 模拟静态方法
        mockStatic(JsonUtil.class);
        mockStatic(MonitorUtil.class);
        
        // 准备测试数据
        when(message.getStringProperty("data")).thenReturn(testJson);
        when(JsonUtil.fromJson(eq(testJson), any())).thenReturn(null);

        // 执行测试
        gaoDeFutureMonitorConsumer.onMessage(message);

        // 验证调用
        verify(message, times(1)).getStringProperty("data");
        verify(geoGateway, never()).queryRoutes(anyInt(), any(List.class));
    }

    @Test
    public void testOnMessage_JsonDeserializationThrowsException() {
        // 模拟静态方法
        mockStatic(JsonUtil.class);
        mockStatic(MonitorUtil.class);
        
        // 准备测试数据
        when(message.getStringProperty("data")).thenReturn("invalid-json");
        when(JsonUtil.fromJson(eq("invalid-json"), any())).thenThrow(new RuntimeException("JSON parse error"));

        // 执行测试 - 不应该抛出异常，应该被catch住
        gaoDeFutureMonitorConsumer.onMessage(message);

        // 验证调用
        verify(message, times(1)).getStringProperty("data");
        verify(geoGateway, never()).queryRoutes(anyInt(), any(List.class));
    }

    @Test
    public void testOnMessage_GeoGatewayReturnsNull() {
        // 模拟静态方法
        mockStatic(JsonUtil.class);
        mockStatic(MonitorUtil.class);
        
        // 准备测试数据
        when(message.getStringProperty("data")).thenReturn(testJson);
        when(geoGateway.queryRoutes(eq(1), any(List.class))).thenReturn(null);
        when(JsonUtil.fromJson(eq(testJson), any())).thenReturn(testGaoDeFutureDTO);

        // 执行测试
        gaoDeFutureMonitorConsumer.onMessage(message);

        // 验证调用
        verify(message, times(1)).getStringProperty("data");
        verify(geoGateway, times(1)).queryRoutes(eq(1), any(List.class));
    }

    @Test
    public void testOnMessage_GeoGatewayReturnsEmptyList() {
        // 模拟静态方法
        mockStatic(JsonUtil.class);
        mockStatic(MonitorUtil.class);
        
        // 准备测试数据
        when(message.getStringProperty("data")).thenReturn(testJson);
        when(geoGateway.queryRoutes(eq(1), any(List.class))).thenReturn(Collections.emptyList());
        when(JsonUtil.fromJson(eq(testJson), any())).thenReturn(testGaoDeFutureDTO);

        // 执行测试
        gaoDeFutureMonitorConsumer.onMessage(message);

        // 验证调用
        verify(message, times(1)).getStringProperty("data");
        verify(geoGateway, times(1)).queryRoutes(eq(1), any(List.class));
    }

    @Test
    public void testOnMessage_GeoGatewayThrowsException() {
        // 模拟静态方法
        mockStatic(JsonUtil.class);
        mockStatic(MonitorUtil.class);
        
        // 准备测试数据
        when(message.getStringProperty("data")).thenReturn(testJson);
        when(geoGateway.queryRoutes(eq(1), any(List.class)))
                .thenThrow(new RuntimeException("GeoGateway error"));
        when(JsonUtil.fromJson(eq(testJson), any())).thenReturn(testGaoDeFutureDTO);

        // 执行测试 - 不应该抛出异常，应该被catch住
        gaoDeFutureMonitorConsumer.onMessage(message);

        // 验证调用
        verify(message, times(1)).getStringProperty("data");
        verify(geoGateway, times(1)).queryRoutes(eq(1), any(List.class));
    }

    @Test
    public void testOnMessage_MessagePropertyIsNull() {
        // 模拟静态方法
        mockStatic(JsonUtil.class);
        mockStatic(MonitorUtil.class);
        
        // 准备测试数据
        when(message.getStringProperty("data")).thenReturn(null);

        // 执行测试
        gaoDeFutureMonitorConsumer.onMessage(message);

        // 验证调用
        verify(message, times(1)).getStringProperty("data");
        verify(geoGateway, never()).queryRoutes(anyInt(), any(List.class));
    }

    @Test
    public void testOnMessage_MessageThrowsException() {
        // 模拟静态方法
        mockStatic(JsonUtil.class);
        mockStatic(MonitorUtil.class);
        
        // 准备测试数据
        when(message.getStringProperty("data"))
                .thenThrow(new RuntimeException("Message property error"));

        // 执行测试 - 不应该抛出异常，应该被catch住
        gaoDeFutureMonitorConsumer.onMessage(message);

        // 验证调用
        verify(message, times(1)).getStringProperty("data");
        verify(geoGateway, never()).queryRoutes(anyInt(), any(List.class));
    }

    @Test
    public void testOnMessage_VerifyPositionParametersCorrectness() {
        // 模拟静态方法
        mockStatic(JsonUtil.class);
        mockStatic(MonitorUtil.class);
        
        // 准备测试数据
        when(message.getStringProperty("data")).thenReturn(testJson);
        when(geoGateway.queryRoutes(eq(1), any(List.class))).thenReturn(Arrays.asList(testRoute));
        when(JsonUtil.fromJson(eq(testJson), any())).thenReturn(testGaoDeFutureDTO);

        // 执行测试
        gaoDeFutureMonitorConsumer.onMessage(message);

        // 使用ArgumentCaptor来验证传递给geoGateway.queryRoutes的参数
        verify(geoGateway, times(1)).queryRoutes(eq(1), argThat(positionList -> {
            if (positionList == null || positionList.size() != 1) {
                return false;
            }
            Position position = (Position) positionList.get(0);
            return Objects.equals(position.getFromLongitude(), 121.5) &&
                   Objects.equals(position.getFromLatitude(), 31.2) &&
                   Objects.equals(position.getToLongitude(), 121.6) &&
                   Objects.equals(position.getToLatitude(), 31.3);
        }));
    }

    @Test
    public void testOnMessage_WithMultipleRoutes() {
        // 模拟静态方法
        mockStatic(JsonUtil.class);
        mockStatic(MonitorUtil.class);
        
        // 测试当geoGateway返回多个路线时，只使用第一个
        Route route1 = new Route("hash1", 10.0, 20.0);
        Route route2 = new Route("hash2", 15.0, 25.0);
        List<Route> routes = Arrays.asList(route1, route2);

        when(message.getStringProperty("data")).thenReturn(testJson);
        when(geoGateway.queryRoutes(eq(1), any(List.class))).thenReturn(routes);
        when(JsonUtil.fromJson(eq(testJson), any())).thenReturn(testGaoDeFutureDTO);

        // 执行测试
        gaoDeFutureMonitorConsumer.onMessage(message);

        // 验证调用
        verify(message, times(1)).getStringProperty("data");
        verify(geoGateway, times(1)).queryRoutes(eq(1), any(List.class));
    }

    @Test
    public void testOnMessage_NumberFormatException() {
        // 模拟静态方法
        mockStatic(JsonUtil.class);
        mockStatic(MonitorUtil.class);
        
        // 测试当数值计算出现NumberFormatException时的处理
        // 创建一个会导致NumberFormatException的DTO
        GaoDeFutureDTO problematicDTO = new GaoDeFutureDTO();
        problematicDTO.setCityId(1);
        problematicDTO.setUserOrderId("test-order-123");
        problematicDTO.setFromLongitude(121.5);
        problematicDTO.setFromLatitude(31.2);
        problematicDTO.setToLongitude(121.6);
        problematicDTO.setToLatitude(31.3);
        problematicDTO.setDepartureTime(new Date());
        // 设置null值可能导致NumberFormatException
        problematicDTO.setRealTimeDistance(null);
        problematicDTO.setRealTimeDuration(null);
        problematicDTO.setFutureDistance(null);
        problematicDTO.setFutureDuration(null);

        when(message.getStringProperty("data")).thenReturn(testJson);
        when(geoGateway.queryRoutes(eq(1), any(List.class))).thenReturn(Arrays.asList(testRoute));
        when(JsonUtil.fromJson(eq(testJson), any())).thenReturn(problematicDTO);

        // 执行测试 - 应该能正常处理异常
        gaoDeFutureMonitorConsumer.onMessage(message);

        // 验证调用
        verify(message, times(1)).getStringProperty("data");
        verify(geoGateway, times(1)).queryRoutes(eq(1), any(List.class));
    }
}