package com.ctrip.dcs.dsp.delay.model;

import com.ctrip.dcs.dsp.delay.consts.CommonConstant;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class Route {

    private String hash;

    private String timePeriodHash;

    private Double distance;

    private Double duration;

    public Route() {
    }

    public Route(String hash, double distance, double duration) {
        this.hash = hash;
        this.distance = distance;
        this.duration = duration;
    }

    public Route(String hash, String timePeriodHash, double distance, double duration) {
        this.hash = hash;
        this.timePeriodHash = timePeriodHash;
        this.distance = distance;
        this.duration = duration;
    }

    public static String toKey(Long taskId, String hash) {
        return CommonConstant.ADJACENCY_INFO_PREFIX + CommonConstant.PLACEHOLDER + CommonConstant.APP_ID + CommonConstant.PLACEHOLDER + taskId + CommonConstant.PLACEHOLDER + hash;
    }

    public static String toValue(Double distance, Double duration) {
        return distance + CommonConstant.PLACEHOLDER + duration;
    }

    public static String toKey(Long taskId, String hash, Long useTime) {
        return toKey(taskId, hash) + CommonConstant.PLACEHOLDER + useTime;
    }

}
