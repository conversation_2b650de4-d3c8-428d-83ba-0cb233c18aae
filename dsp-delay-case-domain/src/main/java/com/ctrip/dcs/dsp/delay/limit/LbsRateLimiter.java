package com.ctrip.dcs.dsp.delay.limit;

import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.MetricsUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.util.concurrent.RateLimiter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 接单限流器
 * <AUTHOR>
 */
@Component
public class LbsRateLimiter {

    private static final Logger logger = LoggerFactory.getLogger(LbsRateLimiter.class);

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    private RateLimiter limiter;

    private RateLimiter futureLimiter;

    @PostConstruct
    public void init() {
        limiter = RateLimiter.create(delayDspCommonQConfig.getLbsPermitsPerSecond());
        futureLimiter = RateLimiter.create(delayDspCommonQConfig.getFuturePermitPerSecond());
    }

    public double acquire() {
        try {
            double acquire = limiter.acquire();
            MetricsUtil.recordTime("lbs.rate.limiter.acquire.time", (long) acquire * 1000);
            return acquire;
        } catch (Exception e) {
            logger.error(e);
        }
        return 0D;
    }

    public boolean futureAcquire() {
        try {
            return futureLimiter.tryAcquire();
        } catch (Exception e) {
            logger.error(e);
        }
        return false;
    }
}
