package com.ctrip.dcs.dsp.delay.consts;

/**
 * <AUTHOR>
 */
public class CommonConstant {

    public static final String APP_ID = "100036007";

    public static final String PLACEHOLDER = "#";
    
    public static final String UNDERLINE = "_";

//  =============================业务常量=============================

    // 司机工作开始、结束前后时间范围
    public static final int DRIVER_WORK_TIME_RANGE_HOUR = 2;

//  =============================缓存前缀=============================

    public static final String GLOBAL_SEQUENCE = "GLOBAL_SEQUENCE";

    public static final String VIRTUAL_DSP_NOT_TAKEN_PREFIX = "VIRTUAL_DSP_NOT_TAKEN_PREFIX";

    public static final String ADJACENCY_INFO_PREFIX = "ADJACENCY_INFO_PREFIX";

    public static final String RPC_CACHE_PREFIX = "RPC_CACHE_PREFIX";

//  =============================消息主题=============================

    public static final String DELAY_DSP_ORDER_INSERT_SUBJECT = "dcs.dsp.delay.order.insert";

    public static final String DELAY_DSP_TASK_RUN_SUBJECT = "dcs.dsp.delay.task.run";

    public static final String DELAY_DSP_RELEASE_INVENTORY_FOR_INSERT_SUBJECT = "dcs.self.transport.inventory.virtual.release";

    public static final String DELAY_DSP_TASK_MATCH_COMPLETE = "dcs.dsp.delay.match.task.complete";

    public static final String DELAY_DSP_OUT_POOL_TIME_UPDATE = "dcs.dsp.delay.out.pool.time.update";

    public static final String DELAY_DSP_MATCH_TASK_RUN_SUBJECT = "dcs.dsp.delay.match.task.run";

    public static final String UPDATE_INVENTORY_FOR_MATCH_TASK_COMPLETE = "dcs.self.transport.inventory.virtual.use";

    public static final String DELAY_DSP_TASK_RUN_CHECK_SUBJECT = "dcs.dsp.delay.task.run.check";

    public static final String DELAY_DSP_MATCH_TASK_RUN_CHECK_SUBJECT = "dcs.dsp.delay.match_task.run.check";

    public static final String DELAY_PRE_OUT_POOL_CHECK = "dcs.dsp.delay.pre.out.pool.check";

    public static final String DELAY_DSP_ORDER_OUT_SUBJECT = "dcs.dsp.delay.order.out";

    public static final String DELAY_DSP_ORDER_OUT_CHECK_SUBJECT = "dcs.dsp.delay.order.out.check";

    public static final String DCS_DSP_DELAY_TASK_ADJACENCY_ROUTE_SUBJECT = "dcs.dsp.delay.task.adjacency.route";

    public static final String ORDER_CANCEL_SUBJECT = "car.qbest.order.orderstate.SUPPLY_ORDER_CANCEL";

    public static final String MAIN_ORDER_CANCEL_SUBJECT = "car.qbest.order.orderstate.ORDER_CANCEL";

    public static final String DSP_ORDER_CANCEL_TOPIC = "dcs.self.dispatchorder.cancel";

    public static final String DSP_ORDER_BOOK_TIME_CHANGE_TOPIC = "dcs.self.driver.order.estimate.time.change";

    public static final String DSP_ORDER_DRIVER_CAR_CONFIRM_TOPIC = "dcs.self.dispatchorder.drivercar.confirm";

    public static final String DELAY_DSP_ORDER_DEADLINE_CHECK_SUBJECT = "dcs.dsp.delay.order.deadline.check";

    public static final String DCS_ORDER_WORK_BENCH_LOG_CREATED = "dcs.order.work.bench.log.created";

    public static final String GAODE_FUTURE_MONITOR_DELAY_SUBJECT = "dcs.dsp.delay.gaode.future.monitor";
}
