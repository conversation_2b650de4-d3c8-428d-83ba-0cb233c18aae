package com.ctrip.dcs.dsp.delay.infrastructure.util;

import org.junit.Test;

import static org.junit.Assert.*;

/**
 * DoubleUtils 单元测试
 * 
 * <AUTHOR>
 */
public class DoubleUtilsTest {

    @Test
    public void testToString_SimpleValue() {
        // 测试简单的double值转换
        double value = 123.456;
        String result = DoubleUtils.toString(value);
        
        assertEquals("123.456", result);
    }

    @Test
    public void testToString_IntegerValue() {
        // 测试整数值转换
        double value = 100.0;
        String result = DoubleUtils.toString(value);
        
        assertEquals("100.0", result);
    }

    @Test
    public void testToString_ZeroValue() {
        // 测试零值转换
        double value = 0.0;
        String result = DoubleUtils.toString(value);
        
        assertEquals("0.0", result);
    }

    @Test
    public void testToString_NegativeValue() {
        // 测试负数转换
        double value = -123.456;
        String result = DoubleUtils.toString(value);
        
        assertEquals("-123.456", result);
    }

    @Test
    public void testToString_VerySmallValue() {
        // 测试很小的数值
        double value = 0.000001;
        String result = DoubleUtils.toString(value);
        
        assertEquals("1.0E-6", result);
    }

    @Test
    public void testToString_VeryLargeValue() {
        // 测试很大的数值
        double value = 1234567890123.456;
        String result = DoubleUtils.toString(value);
        
        assertEquals("1.234567890123456E12", result);
    }

    @Test
    public void testToString_InfinityValue() {
        // 测试无穷大值
        double value = Double.POSITIVE_INFINITY;
        String result = DoubleUtils.toString(value);
        
        assertEquals("Infinity", result);
    }

    @Test
    public void testToString_NegativeInfinityValue() {
        // 测试负无穷大值
        double value = Double.NEGATIVE_INFINITY;
        String result = DoubleUtils.toString(value);
        
        assertEquals("-Infinity", result);
    }

    @Test
    public void testToString_NaNValue() {
        // 测试NaN值
        double value = Double.NaN;
        String result = DoubleUtils.toString(value);
        
        assertEquals("NaN", result);
    }
}
