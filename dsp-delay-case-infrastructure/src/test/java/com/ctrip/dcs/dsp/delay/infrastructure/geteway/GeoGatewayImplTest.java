package com.ctrip.dcs.dsp.delay.infrastructure.geteway;

import com.ctrip.dcs.basic.map.application.service.interfaces.BaseGpsDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.dto.GaoDeFutureDTO;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.DcsMapDomainServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.soa.OchGeoServiceProxy;
import com.ctrip.dcs.dsp.delay.infrastructure.trocks.TRocksProviderProxy;
import com.ctrip.dcs.dsp.delay.limit.LbsRateLimiter;
import com.ctrip.dcs.dsp.delay.model.Position;
import com.ctrip.dcs.dsp.delay.model.Route;
import com.ctrip.dcs.dsp.delay.mq.MessageProducer;
import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.dcs.dsp.delay.util.GeoHashUtil;
import com.ctrip.dcs.location.application.service.interfaces.dto.ExtendInfoDTO;
import com.ctrip.dcs.location.application.service.message.QueryPredictRouteRequestType;
import com.ctrip.dcs.location.application.service.message.QueryPredictRouteResponseType;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.github.benmanes.caffeine.cache.Cache;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * queryRoutesNew方法的单元测试
 * 
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class GeoGatewayImplTest {

    @InjectMocks
    private GeoGatewayImpl geoGatewayImpl;

    @Mock
    private OchGeoServiceProxy ochGeoServiceProxy;

    @Mock
    private TRocksProviderProxy trocksProviderProxy;

    @Mock
    private Cache<String, String> caffeineCache;

    @Mock
    private LbsRateLimiter rateLimiter;

    @Mock
    private DcsMapDomainServiceProxy dcsMapDomainServiceProxy;

    private ExecutorService gaoDeFutureThreadPool;

    private ExecutorService delayGaoDeFutureThreadPool;

    @Mock
    private MessageProducer messageProducer;

    @Mock
    private DelayDspCommonQConfig delayDspCommonQConfig;

    private Position testPosition;
    private List<Position> testPositions;
    private List<Route> testRoutes;
    private Date testDepartureTime;

    @Before
    public void setUp() {
        // 设置测试数据
        testDepartureTime = new Date(System.currentTimeMillis() + 3600000); // 1小时后
        testPosition = new Position();
        testPosition.setFromLongitude(121.5);
        testPosition.setFromLatitude(31.2);
        testPosition.setToLongitude(121.6);
        testPosition.setToLatitude(31.3);
        testPosition.setFromCoordsys("wgs84");
        testPosition.setToCoordsys("wgs84");
        testPosition.setDepartureTime(testDepartureTime);

        testPositions = Arrays.asList(testPosition);
        when(rateLimiter.futureAcquire()).thenReturn(true);
        when(delayDspCommonQConfig.isGaoDeFutureSwitch()).thenReturn(Boolean.TRUE);


        // 创建测试路线
        String fromHash = GeoHashUtil.buildGeoHash(121.5, 31.2);
        String toHash = GeoHashUtil.buildGeoHash(121.6, 31.3);
        String hash = Position.hash(fromHash, toHash);
        String timeHash = Position.timeHash(fromHash, toHash, testDepartureTime);

        Route realTimeRoute = new Route(hash, 10.0, 20.0);
        Route futureRoute = new Route(hash, timeHash, 12.0, 25.0);

        testRoutes = Arrays.asList(realTimeRoute);

        // 创建同步执行的 ExecutorService 来避免异步等待问题
        delayGaoDeFutureThreadPool = new ExecutorService() {
            @Override
            public void shutdown() {}

            @Override
            public List<Runnable> shutdownNow() { return null; }

            @Override
            public boolean isShutdown() { return false; }

            @Override
            public boolean isTerminated() { return false; }

            @Override
            public boolean awaitTermination(long timeout, TimeUnit unit) { return false; }

            @Override
            public <T> Future<T> submit(Callable<T> task) {
                try {
                    T result = task.call();
                    return CompletableFuture.completedFuture(result);
                } catch (Exception e) {
                    CompletableFuture<T> future = new CompletableFuture<>();
                    future.completeExceptionally(e);
                    return future;
                }
            }

            @Override
            public <T> Future<T> submit(Runnable task, T result) {
                task.run();
                return CompletableFuture.completedFuture(result);
            }

            @Override
            public Future<?> submit(Runnable task) {
                task.run();
                return CompletableFuture.completedFuture(null);
            }

            @Override
            public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks) { return null; }

            @Override
            public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit) { return null; }

            @Override
            public <T> T invokeAny(Collection<? extends Callable<T>> tasks) { return null; }

            @Override
            public <T> T invokeAny(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit) { return null; }

            @Override
            public void execute(Runnable command) {
                command.run();
            }
        };

        gaoDeFutureThreadPool = delayGaoDeFutureThreadPool; // 使用同一个实例

        // 注入到被测试对象中
        try {
            java.lang.reflect.Field delayField = GeoGatewayImpl.class.getDeclaredField("delayGaoDeFutureThreadPool");
            delayField.setAccessible(true);
            delayField.set(geoGatewayImpl, delayGaoDeFutureThreadPool);

            java.lang.reflect.Field gaodeField = GeoGatewayImpl.class.getDeclaredField("gaoDeFutureThreadPool");
            gaodeField.setAccessible(true);
            gaodeField.set(geoGatewayImpl, gaoDeFutureThreadPool);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject ExecutorService", e);
        }
    }

    @Test
    public void testQueryRoutesNew_EmptyPositions() {
        // 测试空位置列表
        geoGatewayImpl.queryRoutesNew(1, "order123", Collections.emptyList(), testRoutes);
        
        // 验证没有调用任何外部服务
        verify(dcsMapDomainServiceProxy, never()).queryEstimateRoute(any());
        verify(messageProducer, never()).sendDelayMessage(anyString(), any(), anyLong());
    }

    @Test
    public void testQueryRoutesNew_NullPositions() {
        // 测试null位置列表
        geoGatewayImpl.queryRoutesNew(1, "order123", null, testRoutes);
        
        // 验证没有调用任何外部服务
        verify(dcsMapDomainServiceProxy, never()).queryEstimateRoute(any());
        verify(messageProducer, never()).sendDelayMessage(anyString(), any(), anyLong());
    }

    @Test
    public void testQueryRoutesNew_PositionWithoutDepartureTime() throws Exception {
        // 创建没有出发时间的位置
        Position positionWithoutDepartureTime = new Position();
        positionWithoutDepartureTime.setFromLongitude(121.5);
        positionWithoutDepartureTime.setFromLatitude(31.2);
        positionWithoutDepartureTime.setToLongitude(121.6);
        positionWithoutDepartureTime.setToLatitude(31.3);
        positionWithoutDepartureTime.setFromCoordsys("wgs84");
        positionWithoutDepartureTime.setToCoordsys("wgs84");
        // 注意：没有设置departureTime

        List<Position> positions = Arrays.asList(positionWithoutDepartureTime);

        // 模拟配置
        when(delayDspCommonQConfig.isUseGaodeFuture()).thenReturn(true);
//        when(delayDspCommonQConfig.isTop20CitySwitch()).thenReturn(false);

        // 调用方法
        geoGatewayImpl.queryRoutesNew(1, "order123", positions, testRoutes);

        // 验证调用了queryEstimateRoute但返回null
        verify(dcsMapDomainServiceProxy, never()).queryEstimateRoute(any());
    }

    @Test
    public void testQueryRoutesNew_GaodeFutureDisabled() throws Exception {
        // 模拟高德未来路径功能被禁用
        when(delayDspCommonQConfig.isUseGaodeFuture()).thenReturn(false);

        // 使用反射调用私有方法queryEstimateRoute
        Method queryEstimateRouteMethod = GeoGatewayImpl.class.getDeclaredMethod(
            "queryEstimateRoute", Integer.class, String.class, Position.class);
        queryEstimateRouteMethod.setAccessible(true);

        Route result = (Route) queryEstimateRouteMethod.invoke(
            geoGatewayImpl, 1, "order123", testPosition);

        // 验证返回null
        assertNull(result);
        verify(dcsMapDomainServiceProxy, never()).queryEstimateRoute(any());
    }

    @Test
    public void testQueryRoutesNew_CityNotInTop20() throws Exception {
        // 模拟城市不在top20列表中
        when(delayDspCommonQConfig.isUseGaodeFuture()).thenReturn(true);
        when(delayDspCommonQConfig.isTop20CitySwitch()).thenReturn(true);
        when(delayDspCommonQConfig.top20CityIds()).thenReturn(Arrays.asList(2, 3, 4)); // 不包含cityId=1

        // 使用反射调用私有方法queryEstimateRoute
        Method queryEstimateRouteMethod = GeoGatewayImpl.class.getDeclaredMethod(
            "queryEstimateRoute", Integer.class, String.class, Position.class);
        queryEstimateRouteMethod.setAccessible(true);

        Route result = (Route) queryEstimateRouteMethod.invoke(
            geoGatewayImpl, 1, "order123", testPosition);

        // 验证返回null
        assertNull(result);
        verify(dcsMapDomainServiceProxy, never()).queryEstimateRoute(any());
    }

    @Test
    public void testQueryRoutesNew_SuccessfulMatchAndSendMessage() throws Exception {
        // 模拟配置
        when(delayDspCommonQConfig.isUseGaodeFuture()).thenReturn(true);
        when(delayDspCommonQConfig.isTop20CitySwitch()).thenReturn(false);
        // 模拟响应
        QueryPredictRouteResponseType response = new QueryPredictRouteResponseType();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE);
        response.setResponseResult(responseResult);
        response.setDistance(12000); // 12km
        response.setDuration(1500); // 25分钟

        when(dcsMapDomainServiceProxy.queryEstimateRoute(any())).thenReturn(response);

        // 创建匹配的路线数据
        String fromHash = GeoHashUtil.buildGeoHash(121.5, 31.2);
        String toHash = GeoHashUtil.buildGeoHash(121.6, 31.3);
        String hash = Position.hash(fromHash, toHash);
        String timeHash = Position.timeHash(fromHash, toHash, testDepartureTime);

        Route realTimeRoute = new Route(hash, 10.0, 20.0);
        List<Route> routes = Arrays.asList(realTimeRoute);

        // 调用方法
        geoGatewayImpl.queryRoutesNew(1, "order123", testPositions, routes);

        // 验证调用了dcsMapDomainServiceProxy
        verify(dcsMapDomainServiceProxy, times(1)).queryEstimateRoute(any());

        // 验证请求参数
        ArgumentCaptor<QueryPredictRouteRequestType> requestCaptor = 
            ArgumentCaptor.forClass(QueryPredictRouteRequestType.class);
        verify(dcsMapDomainServiceProxy).queryEstimateRoute(requestCaptor.capture());

        QueryPredictRouteRequestType request = requestCaptor.getValue();
        assertNotNull(request);
        assertEquals("order123", request.getOrderId());
        assertEquals(testDepartureTime.getTime(), request.getDepartureTime().longValue());

        // 验证起点信息
        BaseGpsDTO origin = request.getOrigin();
        assertNotNull(origin);
        assertEquals(BigDecimal.valueOf(31.2), origin.getLatitude());
        assertEquals(BigDecimal.valueOf(121.5), origin.getLongitude());
        assertEquals("wgs84", origin.getCoordType());
        assertEquals(1L, origin.getCityId().longValue());

        // 验证终点信息
        BaseGpsDTO destination = request.getDestination();
        assertNotNull(destination);
        assertEquals(BigDecimal.valueOf(31.3), destination.getLatitude());
        assertEquals(BigDecimal.valueOf(121.6), destination.getLongitude());
        assertEquals("wgs84", destination.getCoordType());
        assertEquals(1L, destination.getCityId().longValue());

        // 验证扩展信息
        ExtendInfoDTO extendInfo = request.getExtendInfoDTO();
        assertNotNull(extendInfo);
        assertTrue(extendInfo.isForceGaodeFuture());

        // 验证发送了延迟消息
        verify(messageProducer, times(1)).sendDelayMessage(anyString(), any(), anyLong());

        // 验证消息内容
        ArgumentCaptor<Map<String, Object>> messageCaptor = ArgumentCaptor.forClass(Map.class);
        ArgumentCaptor<Long> delayTimeCaptor = ArgumentCaptor.forClass(Long.class);
        verify(messageProducer).sendDelayMessage(anyString(), messageCaptor.capture(), delayTimeCaptor.capture());

        Map<String, Object> messageData = messageCaptor.getValue();
        assertNotNull(messageData);
        assertTrue(messageData.containsKey("data"));

        Long delayTime = delayTimeCaptor.getValue();
        assertTrue(delayTime > 0); // 延迟时间应该大于0
    }

    @Test
    public void testQueryRoutesNew_ServiceReturnsNull() throws Exception {
        // 模拟配置
        when(delayDspCommonQConfig.isUseGaodeFuture()).thenReturn(true);
        when(delayDspCommonQConfig.isTop20CitySwitch()).thenReturn(false);

        // 模拟服务返回null
        when(dcsMapDomainServiceProxy.queryEstimateRoute(any())).thenReturn(null);

        // 调用方法
        geoGatewayImpl.queryRoutesNew(1, "order123", testPositions, testRoutes);

        // 验证调用了服务但没有发送消息
        verify(dcsMapDomainServiceProxy, times(1)).queryEstimateRoute(any());
        verify(messageProducer, never()).sendDelayMessage(anyString(), any(), anyLong());
    }

    @Test
    public void testQueryRoutesNew_ServiceReturnsError() throws Exception {
        // 模拟配置
        when(delayDspCommonQConfig.isUseGaodeFuture()).thenReturn(true);
        when(delayDspCommonQConfig.isTop20CitySwitch()).thenReturn(false);

        // 模拟服务返回错误
        QueryPredictRouteResponseType response = new QueryPredictRouteResponseType();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode("ERROR");
        response.setResponseResult(responseResult);

        when(dcsMapDomainServiceProxy.queryEstimateRoute(any())).thenReturn(response);

        // 调用方法
        geoGatewayImpl.queryRoutesNew(1, "order123", testPositions, testRoutes);

        // 验证调用了服务但没有发送消息
        verify(dcsMapDomainServiceProxy, times(1)).queryEstimateRoute(any());
        verify(messageProducer, never()).sendDelayMessage(anyString(), any(), anyLong());
    }

    @Test
    public void testQueryRoutesNew_EmptyResults() throws Exception {
        // 模拟配置
        when(delayDspCommonQConfig.isUseGaodeFuture()).thenReturn(true);
        when(delayDspCommonQConfig.isTop20CitySwitch()).thenReturn(false);

        // 模拟服务返回成功但数据为null
        QueryPredictRouteResponseType response = new QueryPredictRouteResponseType();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE);
        response.setResponseResult(responseResult);
        response.setDistance(null);
        response.setDuration(null);

        when(dcsMapDomainServiceProxy.queryEstimateRoute(any())).thenReturn(response);

        // 调用方法
        geoGatewayImpl.queryRoutesNew(1, "order123", testPositions, testRoutes);

        // 验证调用了服务但没有发送消息
        verify(dcsMapDomainServiceProxy, times(1)).queryEstimateRoute(any());
        verify(messageProducer, never()).sendDelayMessage(anyString(), any(), anyLong());
    }

    @Test
    public void testQueryRoutesNew_NoMatchingRoutes() throws Exception {
        // 模拟配置
        when(delayDspCommonQConfig.isUseGaodeFuture()).thenReturn(true);
        when(delayDspCommonQConfig.isTop20CitySwitch()).thenReturn(false);

        // 模拟响应
        QueryPredictRouteResponseType response = new QueryPredictRouteResponseType();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE);
        response.setResponseResult(responseResult);
        response.setDistance(12000);
        response.setDuration(1500);

        when(dcsMapDomainServiceProxy.queryEstimateRoute(any())).thenReturn(response);

        // 创建不匹配的路线数据（不同的hash）
        Route nonMatchingRoute = new Route("different_hash", 10.0, 20.0);
        List<Route> routes = Arrays.asList(nonMatchingRoute);

        // 调用方法
        geoGatewayImpl.queryRoutesNew(1, "order123", testPositions, routes);

        // 验证调用了服务但没有发送消息（因为没有匹配的路线）
        verify(dcsMapDomainServiceProxy, times(1)).queryEstimateRoute(any());
        verify(messageProducer, never()).sendDelayMessage(anyString(), any(), anyLong());
    }

    @Test
    public void testQueryRoutesNew_ExceptionInAsyncTask() throws Exception {
        // 模拟配置
        when(delayDspCommonQConfig.isUseGaodeFuture()).thenReturn(true);
        when(delayDspCommonQConfig.isTop20CitySwitch()).thenReturn(false);

        // 模拟服务抛出异常
        when(dcsMapDomainServiceProxy.queryEstimateRoute(any()))
            .thenThrow(new RuntimeException("Test exception"));

        // 调用方法
        geoGatewayImpl.queryRoutesNew(1, "order123", testPositions, testRoutes);

        // 验证调用了服务但没有发送消息
        verify(dcsMapDomainServiceProxy, times(1)).queryEstimateRoute(any());
        verify(messageProducer, never()).sendDelayMessage(anyString(), any(), anyLong());
    }

    @Test
    public void testBuildGaoDeInfo() throws Exception {
        // 使用反射测试私有静态方法buildGaoDeInfo
        Method buildGaoDeInfoMethod = GeoGatewayImpl.class.getDeclaredMethod(
            "buildGaoDeInfo", Integer.class, String.class, Position.class, Route.class, Route.class);
        buildGaoDeInfoMethod.setAccessible(true);

        Route realTimeRoute = new Route("hash1", 10.0, 20.0);
        Route futureRoute = new Route("hash1", "timeHash1", 12.0, 25.0);

        GaoDeFutureDTO result = (GaoDeFutureDTO) buildGaoDeInfoMethod.invoke(
            null, 1, "order123", testPosition, realTimeRoute, futureRoute);

        // 验证结果
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getCityId());
        assertEquals("order123", result.getUserOrderId());
        assertEquals(Double.valueOf(121.5), result.getFromLongitude());
        assertEquals(Double.valueOf(31.2), result.getFromLatitude());
        assertEquals(Double.valueOf(121.6), result.getToLongitude());
        assertEquals(Double.valueOf(31.3), result.getToLatitude());
        assertEquals(testDepartureTime, result.getDepartureTime());
        assertEquals(Double.valueOf(10.0), result.getRealTimeDistance());
        assertEquals(Double.valueOf(10.0), result.getRealTimeDuration()); // 注意：代码中有bug，duration用的是distance
        assertEquals(Double.valueOf(12.0), result.getFutureDistance());
        assertEquals(Double.valueOf(25.0), result.getFutureDuration());
    }

    @Test
    public void testQueryRoutesNew_MultiplePositions() throws Exception {
        // 测试多个位置的情况
        Position position2 = new Position();
        position2.setFromLongitude(121.7);
        position2.setFromLatitude(31.4);
        position2.setToLongitude(121.8);
        position2.setToLatitude(31.5);
        position2.setFromCoordsys("wgs84");
        position2.setToCoordsys("wgs84");
        position2.setDepartureTime(testDepartureTime);

        List<Position> positions = Arrays.asList(testPosition, position2);

        // 模拟配置
        when(delayDspCommonQConfig.isUseGaodeFuture()).thenReturn(true);
        when(delayDspCommonQConfig.isTop20CitySwitch()).thenReturn(false);

        // 模拟响应
        QueryPredictRouteResponseType response = new QueryPredictRouteResponseType();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode(ServiceResponseConstants.ResStatus.SUCCESS_CODE);
        response.setResponseResult(responseResult);
        response.setDistance(12000);
        response.setDuration(1500);

        when(dcsMapDomainServiceProxy.queryEstimateRoute(any())).thenReturn(response);

        // 调用方法
        geoGatewayImpl.queryRoutesNew(1, "order123", positions, testRoutes);

        // 验证调用了两次服务（每个位置一次）
        verify(dcsMapDomainServiceProxy, times(2)).queryEstimateRoute(any());
    }
}
