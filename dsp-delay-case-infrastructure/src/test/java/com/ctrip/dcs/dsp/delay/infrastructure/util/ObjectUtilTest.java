package com.ctrip.dcs.dsp.delay.infrastructure.util;

import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.Assert.*;

/**
 * ObjectUtil 单元测试
 * 
 * <AUTHOR>
 */
public class ObjectUtilTest {

    @Test
    public void testObjectToMap_NullObject() throws IllegalAccessException {
        // 测试空对象
        Map<String, String> result = ObjectUtil.objectToMap(null);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testObjectToMap_SimpleObject() throws IllegalAccessException {
        // 创建测试对象
        TestSimpleObject obj = new TestSimpleObject();
        obj.stringField = "test string";
        obj.integerField = 42;
        obj.longField = 1000L;
        obj.booleanField = true;
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertEquals("test string", result.get("stringField"));
        assertEquals("42", result.get("integerField"));
        assertEquals("1000", result.get("longField"));
        assertEquals("true", result.get("booleanField"));
    }

    @Test
    public void testObjectToMap_WithNullFields() throws IllegalAccessException {
        // 创建有null字段的测试对象
        TestSimpleObject obj = new TestSimpleObject();
        obj.stringField = null;
        obj.integerField = null;
        obj.longField = 100L;
        obj.booleanField = null;
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertNull(result.get("stringField"));
        assertNull(result.get("integerField"));
        assertEquals("100", result.get("longField"));
        assertNull(result.get("booleanField"));
    }

    @Test
    public void testObjectToMap_WithBigDecimal() throws IllegalAccessException {
        // 测试BigDecimal字段
        TestComplexObject obj = new TestComplexObject();
        obj.decimalField = new BigDecimal("123.456");
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertEquals("123.456", result.get("decimalField"));
    }

    @Test
    public void testObjectToMap_WithLocalDateTime() throws IllegalAccessException {
        // 测试LocalDateTime字段 - 使用真实的DateUtil.format方法
        TestComplexObject obj = new TestComplexObject();
        obj.dateTimeField = LocalDateTime.of(2023, 12, 25, 10, 30, 45);
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        String dateTimeResult = result.get("dateTimeField");
        assertNotNull(dateTimeResult);
        // 验证格式是否符合预期的日期时间格式
        assertTrue(dateTimeResult.contains("2023-12-25"));
        assertTrue(dateTimeResult.contains("10:30:45"));
    }

    @Test
    public void testObjectToMap_WithStringList() throws IllegalAccessException {
        // 测试List<String>字段
        TestComplexObject obj = new TestComplexObject();
        obj.stringListField = Arrays.asList("apple", "banana", "cherry");
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertEquals("apple,banana,cherry", result.get("stringListField"));
    }

    @Test
    public void testObjectToMap_WithEmptyStringList() throws IllegalAccessException {
        // 测试空的List<String>字段
        TestComplexObject obj = new TestComplexObject();
        obj.stringListField = new ArrayList<>();
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertEquals("", result.get("stringListField"));
    }

    @Test
    public void testObjectToMap_WithSingleItemStringList() throws IllegalAccessException {
        // 测试只有一个元素的List<String>字段
        TestComplexObject obj = new TestComplexObject();
        obj.stringListField = Arrays.asList("single");
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertEquals("single", result.get("stringListField"));
    }

    @Test
    public void testObjectToMap_WithInheritance() throws IllegalAccessException {
        // 测试继承关系
        TestChildObject obj = new TestChildObject();
        obj.parentField = "parent value";
        obj.childField = "child value";
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertEquals("parent value", result.get("parentField"));
        assertEquals("child value", result.get("childField"));
    }

    @Test
    public void testObjectToMap_ComplexObjectWithAllTypes() throws IllegalAccessException {
        // 创建包含所有支持类型的复杂对象
        TestComplexObject obj = new TestComplexObject();
        obj.stringField = "complex test";
        obj.integerField = 999;
        obj.longField = 888888L;
        obj.booleanField = false;
        obj.decimalField = new BigDecimal("999.99");
        obj.dateTimeField = LocalDateTime.of(2023, 1, 1, 12, 0, 0);
        obj.stringListField = Arrays.asList("one", "two", "three");
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertEquals("complex test", result.get("stringField"));
        assertEquals("999", result.get("integerField"));
        assertEquals("888888", result.get("longField"));
        assertEquals("false", result.get("booleanField"));
        assertEquals("999.99", result.get("decimalField"));
        assertNotNull(result.get("dateTimeField"));
        assertTrue(result.get("dateTimeField").contains("2023-01-01"));
        assertEquals("one,two,three", result.get("stringListField"));
    }

    @Test
    public void testObjectToMap_WithUnsupportedType() throws IllegalAccessException {
        // 测试不支持的类型（会被忽略）
        TestUnsupportedTypeObject obj = new TestUnsupportedTypeObject();
        obj.supportedField = "supported";
        obj.unsupportedField = new Date(); // Date类型不被支持
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertEquals("supported", result.get("supportedField"));
        // 不支持的类型字段不应该出现在结果中
        assertFalse(result.containsKey("unsupportedField"));
    }

    @Test
    public void testObjectToMap_WithNullBigDecimal() throws IllegalAccessException {
        // 测试null的BigDecimal字段
        TestComplexObject obj = new TestComplexObject();
        obj.decimalField = null;
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertNull(result.get("decimalField"));
    }

    @Test
    public void testObjectToMap_WithNullLocalDateTime() throws IllegalAccessException {
        // 测试null的LocalDateTime字段
        TestComplexObject obj = new TestComplexObject();
        obj.dateTimeField = null;
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertNull(result.get("dateTimeField"));
    }

    @Test
    public void testObjectToMap_WithNullStringList() throws IllegalAccessException {
        // 测试null的List字段
        TestComplexObject obj = new TestComplexObject();
        obj.stringListField = null;
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertNull(result.get("stringListField"));
    }

    @Test
    public void testObjectToMap_WithZeroValues() throws IllegalAccessException {
        // 测试零值
        TestSimpleObject obj = new TestSimpleObject();
        obj.stringField = "";
        obj.integerField = 0;
        obj.longField = 0L;
        obj.booleanField = false;
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertEquals("", result.get("stringField"));
        assertEquals("0", result.get("integerField"));
        assertEquals("0", result.get("longField"));
        assertEquals("false", result.get("booleanField"));
    }

    @Test
    public void testObjectToMap_WithNegativeValues() throws IllegalAccessException {
        // 测试负值
        TestSimpleObject obj = new TestSimpleObject();
        obj.integerField = -100;
        obj.longField = -999L;
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertEquals("-100", result.get("integerField"));
        assertEquals("-999", result.get("longField"));
    }

    @Test
    public void testObjectToMap_WithSpecialStrings() throws IllegalAccessException {
        // 测试特殊字符串
        TestSimpleObject obj = new TestSimpleObject();
        obj.stringField = "特殊字符: @#$%^&*()";
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertEquals("特殊字符: @#$%^&*()", result.get("stringField"));
    }

    @Test
    public void testObjectToMap_WithBigDecimalZero() throws IllegalAccessException {
        // 测试BigDecimal零值
        TestComplexObject obj = new TestComplexObject();
        obj.decimalField = BigDecimal.ZERO;
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertEquals("0", result.get("decimalField"));
    }

    @Test
    public void testObjectToMap_WithBigDecimalNegative() throws IllegalAccessException {
        // 测试BigDecimal负值
        TestComplexObject obj = new TestComplexObject();
        obj.decimalField = new BigDecimal("-123.45");
        
        Map<String, String> result = ObjectUtil.objectToMap(obj);
        
        assertNotNull(result);
        assertEquals("-123.45", result.get("decimalField"));
    }

    // 测试用的简单对象类
    public static class TestSimpleObject {
        public String stringField;
        public Integer integerField;
        public Long longField;
        public Boolean booleanField;
    }

    // 测试用的复杂对象类
    public static class TestComplexObject {
        public String stringField;
        public Integer integerField;
        public Long longField;
        public Boolean booleanField;
        public BigDecimal decimalField;
        public LocalDateTime dateTimeField;
        public List<String> stringListField;
    }

    // 测试用的父类
    public static class TestParentObject {
        public String parentField;
    }

    // 测试用的子类
    public static class TestChildObject extends TestParentObject {
        public String childField;
    }

    // 测试用的包含不支持类型的对象类
    public static class TestUnsupportedTypeObject {
        public String supportedField;
        public Date unsupportedField; // 不支持的类型
    }
}
