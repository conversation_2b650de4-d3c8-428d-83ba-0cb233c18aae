package com.ctrip.dcs.dsp.delay.infrastructure.dto;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GaoDeFutureDTO {
    private Integer cityId;
    private String userOrderId;
    private Double fromLongitude;
    private Double fromLatitude;
    private String fromCoordsys;
    private Double toLongitude;
    private Double toLatitude;
    private String toCoordsys;
    private Date departureTime;
    private Double realTimeDistance;
    private Double realTimeDuration;
    private Double futureDistance;
    private Double futureDuration;
    private Date executeTime;
}
