package com.ctrip.dcs.dsp.delay.infrastructure.thread;

import com.ctrip.dcs.dsp.delay.qconfig.DelayDspCommonQConfig;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.concurrent.threadpool.ThreadPoolBuilder;
import com.dianping.cat.async.CatAsync;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
@Configuration
public class ThreadPoolConfig {

    @Autowired
    private DelayDspCommonQConfig delayDspCommonQConfig;

    @Bean("driverThreadPool")
    public ExecutorService getDriverThreadPool() {
        return new ThreadPoolBuilder.FixedThreadPoolBuilder()
                .setPoolSize(4)
                .setDaemon(true)
                .setQueueSize(9999999)
                .setThreadNamePrefix("dcs-delay-driver-thread-")
                .build();
    }

    @Bean("takenThreadPool")
    public ExecutorService getTakenThreadPool() {
        return new ThreadPoolBuilder.FixedThreadPoolBuilder()
                .setPoolSize(1)
                .setDaemon(true)
                .setQueueSize(9999999)
                .setThreadNamePrefix("dcs-delay-driver-taken-thread-")
                .build();
    }

    @Bean("redispatchThreadPool")
    public ExecutorService getRedispatchThreadPool() {
        return new ThreadPoolBuilder.FixedThreadPoolBuilder()
                .setPoolSize(1)
                .setDaemon(true)
                .setQueueSize(9999999)
                .setThreadNamePrefix("dcs-delay-driver-redispatch-thread-")
                .build();
    }

    @Bean("gaoDeFutureThreadPool")
    public ExecutorService gaoDeFutureThreadPool() {
        return getPool(
                "delayGaoDeFutureThreadPool",
                delayDspCommonQConfig.getGaoDeFutureThreadCorePoolSize(),
                delayDspCommonQConfig.getGaoDeFutureThreadMaxPoolSize(),
                delayDspCommonQConfig.getDelayQueueSize());
    }

    @Bean("delayGaoDeFutureThreadPool")
    public ExecutorService delayGaoDeFutureThreadPool() {
        return  getPool(
                "delayGaoDeFutureThreadPool",
                delayDspCommonQConfig.getDelayGaoDeFutureThreadCorePoolSize(),
                delayDspCommonQConfig.getDelayGaoDeFutureThreadMaxPoolSize(),
                delayDspCommonQConfig.getDelayQueueSize());
    }

    protected ExecutorService getPool(String poolName, int coreSize, int maxSize, int workQueueSize) {
        return ThreadPoolBuilder.pool().setCoreSize(coreSize).setMaxSize(maxSize).setDaemon(false).setKeepAliveSecs(50)
                .setUseTtl(true).setWorkQueue(new ArrayBlockingQueue<>(workQueueSize))
                .setThreadNamePrefix(poolName).setRejectHanlder((r, executor) -> {
                    LoggerFactory.getLogger(getClass()).warn("ThreadPool", "Task " + r.toString() + " rejected from " + executor.toString());
                    // 超出队列上限在主线程中同步执行
                    r.run();
                }).build();
    }
}
